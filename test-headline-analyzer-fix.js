/**
 * Test script to verify the Headline Analyzer fix
 * This script tests the API endpoint and verifies the fix is working
 */

async function testHeadlineAnalyzer() {
  console.log('🧪 Testing Headline Analyzer Fix...');
  
  const testCases = [
    {
      name: 'Basic English Headline',
      data: {
        headline: 'How to Boost Your Website Traffic in 2024',
        content_type: 'blog',
        audience_context: 'Digital marketers and website owners'
      }
    },
    {
      name: 'Spanish Headline',
      data: {
        headline: 'Cómo Aumentar el Tráfico de tu Sitio Web en 2024',
        content_type: 'blog',
        audience_context: 'Marketers digitales y propietarios de sitios web'
      }
    },
    {
      name: 'Question Headline',
      data: {
        headline: '¿Quieres Duplicar tus Ventas Online?',
        content_type: 'facebook_ad',
        audience_context: 'Emprendedores y pequeños negocios'
      }
    },
    {
      name: 'Short Headline',
      data: {
        headline: 'Marketing Digital',
        content_type: 'blog'
      }
    }
  ];

  const results = [];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Headline: "${testCase.data.headline}"`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('/api/analyze-headline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        },
        body: JSON.stringify(testCase.data)
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      
      results.push({
        testCase: testCase.name,
        success: true,
        duration,
        overallScore: data.overall_score,
        status: data.status,
        processingTime: data.processing_time
      });

      console.log(`   ✅ Success! Score: ${data.overall_score}/100, Duration: ${duration}ms`);
      console.log(`   📊 Processing Time: ${data.processing_time?.toFixed(2)}s`);
      
    } catch (error) {
      results.push({
        testCase: testCase.name,
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      });

      console.log(`   ❌ Failed: ${error.message}`);
    }
  }

  // Summary
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful tests: ${successful.length}/${results.length}`);
  console.log(`❌ Failed tests: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    const avgScore = successful.reduce((sum, r) => sum + r.overallScore, 0) / successful.length;
    const avgDuration = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
    console.log(`📊 Average Score: ${avgScore.toFixed(1)}/100`);
    console.log(`⏱️ Average Response Time: ${avgDuration.toFixed(0)}ms`);
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Tests Details:');
    failed.forEach(result => {
      console.log(`   - ${result.testCase}: ${result.error}`);
    });
  }

  return {
    totalTests: results.length,
    successful: successful.length,
    failed: failed.length,
    results
  };
}

// Test error handling
async function testErrorHandling() {
  console.log('\n🚨 Testing Error Handling...');
  
  const errorTests = [
    {
      name: 'Empty Headline',
      data: { headline: '', content_type: 'blog' }
    },
    {
      name: 'Too Short Headline',
      data: { headline: 'Hi', content_type: 'blog' }
    },
    {
      name: 'Very Long Headline',
      data: { 
        headline: 'A'.repeat(600), // Exceeds 500 char limit
        content_type: 'blog' 
      }
    }
  ];

  for (const test of errorTests) {
    console.log(`\n🧪 Testing: ${test.name}`);
    
    try {
      const response = await fetch('/api/analyze-headline', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(test.data)
      });

      if (response.ok) {
        console.log(`   ⚠️ Expected error but got success`);
      } else {
        const errorData = await response.json();
        console.log(`   ✅ Correctly handled error: ${response.status} - ${errorData.message || errorData.error}`);
      }
    } catch (error) {
      console.log(`   ✅ Network error handled: ${error.message}`);
    }
  }
}

// Run tests when script is loaded
if (typeof window !== 'undefined') {
  // Browser environment
  window.testHeadlineAnalyzer = testHeadlineAnalyzer;
  window.testErrorHandling = testErrorHandling;
  
  console.log('🧪 Headline Analyzer Test Suite Loaded');
  console.log('Run testHeadlineAnalyzer() to test the API');
  console.log('Run testErrorHandling() to test error scenarios');
} else {
  // Node.js environment
  module.exports = { testHeadlineAnalyzer, testErrorHandling };
}
